const { useState, useEffect, createContext, useContext } = React;

// Create Context for global state
const AppContext = createContext();

const useAppContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within AppProvider');
  }
  return context;
};

const AppProvider = ({ children }) => {
  const [jobs, setJobs] = useState([]);
  const [tags, setTags] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [view, setView] = useState('table'); // 'table' or 'cards'
  const [showForm, setShowForm] = useState(false);
  const [editingJob, setEditingJob] = useState(null);
  const [filters, setFilters] = useState({
    status: '',
    search: '',
    tags: [],
    company: '',
    dateFrom: '',
    dateTo: ''
  });

  const value = {
    jobs, setJobs,
    tags, setTags,
    loading, setLoading,
    error, setError,
    view, setView,
    showForm, setShowForm,
    editingJob, setEditingJob,
    filters, setFilters
  };

  return React.createElement(AppContext.Provider, { value }, children);
};

const App = () => {
  const {
    jobs, setJobs,
    tags, setTags,
    loading, setLoading,
    error, setError,
    showForm, setShowForm,
    editingJob, setEditingJob,
    filters
  } = useAppContext();

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    loadJobs();
  }, [filters]);

  const loadInitialData = async () => {
    setLoading(true);
    try {
      await Promise.all([loadJobs(), loadTags()]);
    } catch (err) {
      setError('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const loadJobs = async () => {
    try {
      const params = new URLSearchParams();
      if (filters.status) params.append('status', filters.status);
      if (filters.search) params.append('search', filters.search);
      if (filters.company) params.append('company', filters.company);
      if (filters.dateFrom) params.append('date_from', filters.dateFrom);
      if (filters.dateTo) params.append('date_to', filters.dateTo);
      filters.tags.forEach(tagId => params.append('tags', tagId));

      const response = await api.get(`/jobs?${params.toString()}`);
      setJobs(response.data);
    } catch (err) {
      setError('Failed to load jobs');
    }
  };

  const loadTags = async () => {
    try {
      const response = await api.get('/tags');
      setTags(response.data);
    } catch (err) {
      setError('Failed to load tags');
    }
  };

  const handleCreateJob = async (jobData) => {
    try {
      setLoading(true);
      console.log('Creating job with data:', jobData);
      await api.post('/jobs', jobData);
      await loadJobs();
      setShowForm(false);
      setError(null);
    } catch (err) {
      console.error('Error creating job:', err);
      setError(`Failed to create job application: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateJob = async (jobId, jobData) => {
    try {
      setLoading(true);
      await api.put(`/jobs/${jobId}`, jobData);
      await loadJobs();
      setEditingJob(null);
      setShowForm(false);
      setError(null);
    } catch (err) {
      setError('Failed to update job application');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteJob = async (jobId) => {
    if (!confirm('Are you sure you want to delete this job application?')) {
      return;
    }

    try {
      setLoading(true);
      await api.delete(`/jobs/${jobId}`);
      await loadJobs();
      setError(null);
    } catch (err) {
      setError('Failed to delete job application');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTag = async (tagData) => {
    try {
      await api.post('/tags', tagData);
      await loadTags();
      setError(null);
    } catch (err) {
      setError('Failed to create tag');
    }
  };

  const handleExportCSV = async () => {
    try {
      const response = await api.get('/export/csv', { responseType: 'blob' });
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `job_applications_${new Date().toISOString().split('T')[0]}.csv`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      setError('Failed to export CSV');
    }
  };

  const editJob = (job) => {
    setEditingJob(job);
    setShowForm(true);
  };

  const closeForm = () => {
    setShowForm(false);
    setEditingJob(null);
  };

  return React.createElement('div', { className: 'min-h-screen bg-gray-50' },
    React.createElement(Header, {
      onNewJob: () => setShowForm(true),
      onExportCSV: handleExportCSV,
      loading
    }),

    error && React.createElement('div', { className: 'container-responsive' },
      React.createElement('div', {
        className: 'bg-red-50 border-l-4 border-red-400 p-4 mb-4 rounded-r-md slide-up'
      },
        React.createElement('div', { className: 'flex flex-col sm:flex-row sm:items-center sm:justify-between' },
          React.createElement('p', { className: 'text-sm text-red-700 mb-2 sm:mb-0' }, error),
          React.createElement('button', {
            onClick: () => setError(null),
            className: 'text-sm text-red-600 hover:text-red-800 hover:bg-red-100 px-3 py-1 rounded transition-all duration-200 touch-target self-start sm:self-auto'
          }, 'Dismiss')
        )
      )
    ),

    React.createElement('div', { className: 'container-responsive py-6 lg:py-8' },
      React.createElement(FilterBar, {
        tags,
        onCreateTag: handleCreateTag,
        loading
      }),

      React.createElement(JobList, {
        jobs,
        onEdit: editJob,
        onDelete: handleDeleteJob,
        loading
      })
    ),

    showForm && React.createElement(JobForm, {
      job: editingJob,
      tags,
      onSubmit: editingJob ?
        (data) => handleUpdateJob(editingJob.id, data) :
        handleCreateJob,
      onClose: closeForm,
      loading
    })
  );
};

const AppWithProvider = () => {
  return React.createElement(AppProvider, null,
    React.createElement(App)
  );
};

ReactDOM.render(React.createElement(AppWithProvider), document.getElementById('root'));
