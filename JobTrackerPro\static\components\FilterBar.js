const FilterBar = ({ tags, onCreateTag, loading }) => {
  const { filters, setFilters } = useAppContext();
  const [showTagForm, setShowTagForm] = useState(false);
  const [newTagName, setNewTagName] = useState('');
  const [newTagColor, setNewTagColor] = useState('#2563EB');

  const statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'Applied', label: 'Applied' },
    { value: 'Interviewing', label: 'Interviewing' },
    { value: 'Offer', label: 'Offer' },
    { value: 'Rejected', label: 'Rejected' },
    { value: 'Withdrawn', label: 'Withdrawn' }
  ];

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleTagToggle = (tagId) => {
    setFilters(prev => ({
      ...prev,
      tags: prev.tags.includes(tagId)
        ? prev.tags.filter(id => id !== tagId)
        : [...prev.tags, tagId]
    }));
  };

  const handleCreateTag = async (e) => {
    e.preventDefault();
    if (!newTagName.trim()) return;

    try {
      await onCreateTag({ name: newTagName.trim(), color: newTagColor });
      setNewTagName('');
      setNewTagColor('#2563EB');
      setShowTagForm(false);
    } catch (err) {
      console.error('Failed to create tag:', err);
    }
  };

  const clearFilters = () => {
    setFilters({
      status: '',
      search: '',
      tags: [],
      company: '',
      dateFrom: '',
      dateTo: ''
    });
  };

  const hasActiveFilters = Object.values(filters).some(value =>
    Array.isArray(value) ? value.length > 0 : value !== ''
  );

  return React.createElement('div', { className: 'card p-4 lg:p-6 mb-6 fade-in' },
    // Main filters section
    React.createElement('div', { className: 'space-y-4 mb-6' },
      // Search - Full width on mobile
      React.createElement('div', { className: 'w-full' },
        React.createElement('input', {
          type: 'text',
          placeholder: 'Search jobs...',
          value: filters.search,
          onChange: (e) => handleFilterChange('search', e.target.value),
          className: 'form-input w-full'
        })
      ),

      // Filters row - Stack on mobile, row on desktop
      React.createElement('div', { className: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4' },
        // Status filter
        React.createElement('div', null,
          React.createElement('label', { className: 'form-label' }, 'Status'),
          React.createElement('select', {
            value: filters.status,
            onChange: (e) => handleFilterChange('status', e.target.value),
            className: 'form-select'
          },
            statusOptions.map(option =>
              React.createElement('option', { key: option.value, value: option.value }, option.label)
            )
          )
        ),

        // Company filter
        React.createElement('div', null,
          React.createElement('label', { className: 'form-label' }, 'Company'),
          React.createElement('input', {
            type: 'text',
            placeholder: 'Filter by company...',
            value: filters.company,
            onChange: (e) => handleFilterChange('company', e.target.value),
            className: 'form-input'
          })
        ),

        // Date from filter
        React.createElement('div', null,
          React.createElement('label', { className: 'form-label' }, 'From Date'),
          React.createElement('input', {
            type: 'date',
            value: filters.dateFrom,
            onChange: (e) => handleFilterChange('dateFrom', e.target.value),
            className: 'form-input'
          })
        ),

        // Date to filter
        React.createElement('div', null,
          React.createElement('label', { className: 'form-label' }, 'To Date'),
          React.createElement('input', {
            type: 'date',
            value: filters.dateTo,
            onChange: (e) => handleFilterChange('dateTo', e.target.value),
            className: 'form-input'
          })
        )
      ),

      // Clear filters button
      hasActiveFilters && React.createElement('div', { className: 'flex justify-end' },
        React.createElement('button', {
          onClick: clearFilters,
          className: 'px-4 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-all duration-200 touch-target'
        }, 'Clear All Filters')
      )
    ),

    // Tags section
    React.createElement('div', {
      className: 'border-t pt-4 transition-colors duration-300',
      style: { borderColor: 'hsl(var(--border))' }
    },
      React.createElement('div', { className: 'flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-4' },
        React.createElement('span', {
          className: 'text-sm font-medium transition-colors duration-300',
          style: { color: 'hsl(var(--foreground))' }
        }, 'Filter by tags:'),
        React.createElement('button', {
          onClick: () => setShowTagForm(!showTagForm),
          className: 'inline-flex items-center px-3 py-2 text-sm rounded-md transition-all duration-200 touch-target mobile-full sm:w-auto',
          style: {
            color: 'hsl(var(--primary))',
            backgroundColor: 'hsl(var(--primary) / 0.1)'
          }
        }, '+ Add Tag')
      ),

      // Tag creation form
      showTagForm && React.createElement('form', {
        onSubmit: handleCreateTag,
        className: 'grid grid-cols-1 sm:grid-cols-4 gap-3 mb-4 p-4 rounded-lg slide-up transition-colors duration-300',
        style: { backgroundColor: 'hsl(var(--muted))' }
      },
        React.createElement('div', { className: 'sm:col-span-2' },
          React.createElement('input', {
            type: 'text',
            placeholder: 'Tag name',
            value: newTagName,
            onChange: (e) => setNewTagName(e.target.value),
            className: 'form-input w-full',
            required: true
          })
        ),
        React.createElement('div', { className: 'flex items-center gap-2' },
          React.createElement('input', {
            type: 'color',
            value: newTagColor,
            onChange: (e) => setNewTagColor(e.target.value),
            className: 'w-12 h-12 rounded cursor-pointer transition-colors duration-300',
            style: { border: '1px solid hsl(var(--border))' }
          }),
          React.createElement('span', {
            className: 'text-sm transition-colors duration-300',
            style: { color: 'hsl(var(--muted-foreground))' }
          }, 'Color')
        ),
        React.createElement('div', { className: 'flex gap-2' },
          React.createElement('button', {
            type: 'submit',
            disabled: loading,
            className: 'btn-primary text-sm disabled:opacity-50 flex-1'
          }, 'Add'),
          React.createElement('button', {
            type: 'button',
            onClick: () => setShowTagForm(false),
            className: 'px-3 py-2 text-sm rounded-md transition-all duration-200 flex-1',
            style: {
              color: 'hsl(var(--muted-foreground))',
              backgroundColor: 'hsl(var(--accent))'
            }
          }, 'Cancel')
        )
      ),

      // Tags list
      React.createElement('div', { className: 'flex flex-wrap gap-2' },
        tags.map(tag =>
          React.createElement('button', {
            key: tag.id,
            onClick: () => handleTagToggle(tag.id),
            className: `tag touch-target transition-all duration-200 ${filters.tags.includes(tag.id)
              ? 'ring-2 ring-blue-500 ring-offset-1'
              : 'hover:opacity-80 hover:scale-105'
              }`,
            style: {
              backgroundColor: `${tag.color}20`,
              color: tag.color,
              border: `1px solid ${tag.color}40`
            }
          }, tag.name)
        ),
        tags.length === 0 && React.createElement('div', {
          className: 'text-sm italic py-4 text-center w-full transition-colors duration-300',
          style: { color: 'hsl(var(--muted-foreground))' }
        }, 'No tags created yet. Create your first tag to organize your applications!')
      )
    )
  );
};
