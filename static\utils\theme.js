// Theme management utility for JobTrackerPro
const ThemeManager = {
  // Theme constants
  THEMES: {
    LIGHT: 'light',
    DARK: 'dark'
  },

  STORAGE_KEY: 'jobtracker-theme',

  // Initialize theme system
  init() {
    this.applyTheme(this.getTheme());
    this.setupSystemThemeListener();
  },

  // Get current theme from localStorage or system preference
  getTheme() {
    // Check localStorage first
    const savedTheme = localStorage.getItem(this.STORAGE_KEY);
    if (savedTheme && Object.values(this.THEMES).includes(savedTheme)) {
      return savedTheme;
    }

    // Fall back to system preference
    return this.getSystemTheme();
  },

  // Get system theme preference
  getSystemTheme() {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return this.THEMES.DARK;
    }
    return this.THEMES.LIGHT;
  },

  // Set theme and persist to localStorage
  setTheme(theme) {
    if (!Object.values(this.THEMES).includes(theme)) {
      console.warn(`Invalid theme: ${theme}`);
      return;
    }

    localStorage.setItem(this.STORAGE_KEY, theme);
    this.applyTheme(theme);
    this.notifyThemeChange(theme);
  },

  // Apply theme to document
  applyTheme(theme) {
    const root = document.documentElement;
    
    if (theme === this.THEMES.DARK) {
      root.setAttribute('data-theme', 'dark');
    } else {
      root.removeAttribute('data-theme');
    }
  },

  // Toggle between light and dark themes
  toggleTheme() {
    const currentTheme = this.getTheme();
    const newTheme = currentTheme === this.THEMES.DARK ? this.THEMES.LIGHT : this.THEMES.DARK;
    this.setTheme(newTheme);
    return newTheme;
  },

  // Check if current theme is dark
  isDark() {
    return this.getTheme() === this.THEMES.DARK;
  },

  // Setup listener for system theme changes
  setupSystemThemeListener() {
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      mediaQuery.addEventListener('change', (e) => {
        // Only update if user hasn't manually set a preference
        const savedTheme = localStorage.getItem(this.STORAGE_KEY);
        if (!savedTheme) {
          const systemTheme = e.matches ? this.THEMES.DARK : this.THEMES.LIGHT;
          this.applyTheme(systemTheme);
          this.notifyThemeChange(systemTheme);
        }
      });
    }
  },

  // Notify theme change to listeners
  notifyThemeChange(theme) {
    const event = new CustomEvent('themechange', {
      detail: { theme }
    });
    window.dispatchEvent(event);
  },

  // Add theme change listener
  onThemeChange(callback) {
    window.addEventListener('themechange', (e) => {
      callback(e.detail.theme);
    });
  },

  // Remove theme change listener
  offThemeChange(callback) {
    window.removeEventListener('themechange', callback);
  },

  // Get theme icon for UI
  getThemeIcon(theme = null) {
    const currentTheme = theme || this.getTheme();
    return currentTheme === this.THEMES.DARK ? '☀️' : '🌙';
  },

  // Get theme label for UI
  getThemeLabel(theme = null) {
    const currentTheme = theme || this.getTheme();
    return currentTheme === this.THEMES.DARK ? 'Light Mode' : 'Dark Mode';
  }
};

// Initialize theme system when script loads
if (typeof window !== 'undefined') {
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      ThemeManager.init();
    });
  } else {
    ThemeManager.init();
  }
}

// Export for use in other modules
window.ThemeManager = ThemeManager;
