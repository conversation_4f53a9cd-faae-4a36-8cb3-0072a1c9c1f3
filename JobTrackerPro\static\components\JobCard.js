const JobCard = ({ job, onEdit, onDelete }) => {
  const getStatusColor = (status) => {
    const colors = {
      'Applied': 'bg-blue-100 text-blue-800',
      'Interviewing': 'bg-yellow-100 text-yellow-800',
      'Offer': 'bg-green-100 text-green-800',
      'Rejected': 'bg-red-100 text-red-800',
      'Withdrawn': 'bg-gray-100 text-gray-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString();
  };

  return React.createElement('div', { className: 'card p-4 lg:p-6 fade-in' },
    React.createElement('div', { className: 'flex flex-col sm:flex-row sm:justify-between sm:items-start mb-4 space-y-2 sm:space-y-0' },
      React.createElement('div', { className: 'flex-1 min-w-0' },
        React.createElement('h3', { className: 'text-lg font-semibold text-gray-900 mb-1 truncate' },
          job.position
        ),
        React.createElement('p', { className: 'text-gray-600 mb-2 font-medium' }, job.company),
        job.location && React.createElement('p', { className: 'text-sm text-gray-500 mb-2' },
          job.location
        )
      ),
      React.createElement('div', { className: 'flex-shrink-0' },
        React.createElement('span', {
          className: `status-badge ${getStatusColor(job.status)}`
        }, job.status)
      )
    ),

    // Tags
    job.tags && job.tags.length > 0 && React.createElement('div', { className: 'mb-4' },
      React.createElement('div', { className: 'flex flex-wrap gap-1' },
        job.tags.map(tag =>
          React.createElement('span', {
            key: tag.id,
            className: 'tag',
            style: {
              backgroundColor: `${tag.color}20`,
              color: tag.color,
              border: `1px solid ${tag.color}40`
            }
          }, tag.name)
        )
      )
    ),

    // Additional info
    React.createElement('div', { className: 'space-y-2 mb-4' },
      job.salary_range && React.createElement('div', { className: 'text-sm' },
        React.createElement('span', { className: 'font-medium text-gray-700' }, 'Salary: '),
        React.createElement('span', { className: 'text-gray-600' }, job.salary_range)
      ),
      job.application_date && React.createElement('div', { className: 'text-sm' },
        React.createElement('span', { className: 'font-medium text-gray-700' }, 'Applied: '),
        React.createElement('span', { className: 'text-gray-600' }, formatDate(job.application_date))
      ),
      job.contact_person && React.createElement('div', { className: 'text-sm' },
        React.createElement('span', { className: 'font-medium text-gray-700' }, 'Contact: '),
        React.createElement('span', { className: 'text-gray-600' }, job.contact_person)
      )
    ),

    // Notes preview
    job.notes && React.createElement('div', { className: 'mb-4' },
      React.createElement('p', { className: 'text-sm text-gray-600 line-clamp-2' },
        job.notes.length > 100 ? `${job.notes.substring(0, 100)}...` : job.notes
      )
    ),

    // Actions
    React.createElement('div', { className: 'flex flex-col sm:flex-row sm:justify-between sm:items-center pt-4 border-t border-gray-200 space-y-3 sm:space-y-0' },
      React.createElement('div', { className: 'flex flex-wrap gap-2' },
        job.job_url && React.createElement('a', {
          href: job.job_url,
          target: '_blank',
          rel: 'noopener noreferrer',
          className: 'inline-flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-all duration-200 touch-target'
        }, 'View Job'),
        job.contact_email && React.createElement('a', {
          href: `mailto:${job.contact_email}`,
          className: 'inline-flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-all duration-200 touch-target'
        }, 'Email Contact')
      ),
      React.createElement('div', { className: 'flex gap-2' },
        React.createElement('button', {
          onClick: () => onEdit(job),
          className: 'flex-1 sm:flex-none px-3 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-all duration-200 touch-target'
        }, 'Edit'),
        React.createElement('button', {
          onClick: () => onDelete(job.id),
          className: 'flex-1 sm:flex-none px-3 py-2 text-sm text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-all duration-200 touch-target'
        }, 'Delete')
      )
    ),

    React.createElement('div', { className: 'text-xs text-gray-400 mt-3 text-center sm:text-left' },
      `Last updated: ${formatDate(job.last_updated)}`
    )
  );
};
