const JobCard = ({ job, onEdit, onDelete }) => {
  const getStatusColor = (status) => {
    const colors = {
      'Applied': 'bg-blue-100 text-blue-800',
      'Interviewing': 'bg-yellow-100 text-yellow-800',
      'Offer': 'bg-green-100 text-green-800',
      'Rejected': 'bg-red-100 text-red-800',
      'Withdrawn': 'bg-gray-100 text-gray-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString();
  };

  return React.createElement('div', { className: 'card p-4 lg:p-6 fade-in' },
    React.createElement('div', { className: 'flex flex-col sm:flex-row sm:justify-between sm:items-start mb-4 space-y-2 sm:space-y-0' },
      React.createElement('div', { className: 'flex-1 min-w-0' },
        React.createElement('h3', {
          className: 'text-lg font-semibold mb-1 truncate transition-colors duration-300',
          style: { color: 'hsl(var(--foreground))' }
        },
          job.position
        ),
        React.createElement('p', {
          className: 'mb-2 font-medium transition-colors duration-300',
          style: { color: 'hsl(var(--muted-foreground))' }
        }, job.company),
        job.location && React.createElement('p', {
          className: 'text-sm mb-2 transition-colors duration-300',
          style: { color: 'hsl(var(--muted-foreground))' }
        },
          job.location
        )
      ),
      React.createElement('div', { className: 'flex-shrink-0' },
        React.createElement('span', {
          className: `status-badge ${getStatusColor(job.status)}`
        }, job.status)
      )
    ),

    // Tags
    job.tags && job.tags.length > 0 && React.createElement('div', { className: 'mb-4' },
      React.createElement('div', { className: 'flex flex-wrap gap-1' },
        job.tags.map(tag =>
          React.createElement('span', {
            key: tag.id,
            className: 'tag',
            style: {
              backgroundColor: `${tag.color}20`,
              color: tag.color,
              border: `1px solid ${tag.color}40`
            }
          }, tag.name)
        )
      )
    ),

    // Additional info
    React.createElement('div', { className: 'space-y-2 mb-4' },
      job.salary_range && React.createElement('div', { className: 'text-sm' },
        React.createElement('span', {
          className: 'font-medium transition-colors duration-300',
          style: { color: 'hsl(var(--foreground))' }
        }, 'Salary: '),
        React.createElement('span', {
          className: 'transition-colors duration-300',
          style: { color: 'hsl(var(--muted-foreground))' }
        }, job.salary_range)
      ),
      job.application_date && React.createElement('div', { className: 'text-sm' },
        React.createElement('span', {
          className: 'font-medium transition-colors duration-300',
          style: { color: 'hsl(var(--foreground))' }
        }, 'Applied: '),
        React.createElement('span', {
          className: 'transition-colors duration-300',
          style: { color: 'hsl(var(--muted-foreground))' }
        }, formatDate(job.application_date))
      ),
      job.contact_person && React.createElement('div', { className: 'text-sm' },
        React.createElement('span', {
          className: 'font-medium transition-colors duration-300',
          style: { color: 'hsl(var(--foreground))' }
        }, 'Contact: '),
        React.createElement('span', {
          className: 'transition-colors duration-300',
          style: { color: 'hsl(var(--muted-foreground))' }
        }, job.contact_person)
      )
    ),

    // Notes preview
    job.notes && React.createElement('div', { className: 'mb-4' },
      React.createElement('p', {
        className: 'text-sm line-clamp-2 transition-colors duration-300',
        style: { color: 'hsl(var(--muted-foreground))' }
      },
        job.notes.length > 100 ? `${job.notes.substring(0, 100)}...` : job.notes
      )
    ),

    // Actions
    React.createElement('div', {
      className: 'flex flex-col sm:flex-row sm:justify-between sm:items-center pt-4 border-t space-y-3 sm:space-y-0 transition-colors duration-300',
      style: { borderColor: 'hsl(var(--border))' }
    },
      React.createElement('div', { className: 'flex flex-wrap gap-2' },
        job.job_url && React.createElement('a', {
          href: job.job_url,
          target: '_blank',
          rel: 'noopener noreferrer',
          className: 'inline-flex items-center px-3 py-1 text-sm rounded-md transition-all duration-200 touch-target',
          style: {
            color: 'hsl(var(--primary))',
            backgroundColor: 'hsl(var(--primary) / 0.1)'
          }
        }, 'View Job'),
        job.contact_email && React.createElement('a', {
          href: `mailto:${job.contact_email}`,
          className: 'inline-flex items-center px-3 py-1 text-sm rounded-md transition-all duration-200 touch-target',
          style: {
            color: 'hsl(var(--primary))',
            backgroundColor: 'hsl(var(--primary) / 0.1)'
          }
        }, 'Email Contact')
      ),
      React.createElement('div', { className: 'flex gap-2' },
        React.createElement('button', {
          onClick: () => onEdit(job),
          className: 'flex-1 sm:flex-none px-3 py-2 text-sm rounded-md transition-all duration-200 touch-target',
          style: {
            color: 'hsl(var(--muted-foreground))',
            backgroundColor: 'hsl(var(--muted))'
          }
        }, 'Edit'),
        React.createElement('button', {
          onClick: () => onDelete(job.id),
          className: 'flex-1 sm:flex-none px-3 py-2 text-sm rounded-md transition-all duration-200 touch-target',
          style: {
            color: 'hsl(var(--danger))',
            backgroundColor: 'hsl(var(--danger) / 0.1)'
          }
        }, 'Delete')
      )
    ),

    React.createElement('div', {
      className: 'text-xs mt-3 text-center sm:text-left transition-colors duration-300',
      style: { color: 'hsl(var(--muted-foreground))' }
    },
      `Last updated: ${formatDate(job.last_updated)}`
    )
  );
};
