const Header = ({ onNewJob, onExportCSV, loading }) => {
  const { view, setView, jobs } = useAppContext();
  const [statistics, setStatistics] = useState(null);

  useEffect(() => {
    loadStatistics();
  }, [jobs]);

  const loadStatistics = async () => {
    try {
      const response = await api.get('/statistics');
      setStatistics(response.data);
    } catch (err) {
      console.error('Failed to load statistics:', err);
    }
  };

  return React.createElement('header', { className: 'bg-white shadow-sm border-b sticky top-0 z-50' },
    React.createElement('div', { className: 'container-responsive' },
      React.createElement('div', { className: 'flex flex-col lg:flex-row lg:justify-between lg:items-center py-4 lg:py-6 space-y-4 lg:space-y-0' },
        // Title and Statistics Section
        React.createElement('div', { className: 'flex-1' },
          React.createElement('h1', { className: 'text-2xl lg:text-3xl font-bold text-gray-900 mb-2 lg:mb-0' },
            'Job Application Tracker'
          ),
          statistics && React.createElement('div', { className: 'grid grid-cols-2 lg:flex lg:space-x-6 gap-2 lg:gap-0 mt-2 text-sm text-gray-600' },
            React.createElement('span', { className: 'font-medium' }, `Total: ${statistics.total_applications}`),
            React.createElement('span', { className: 'font-medium' }, `This Month: ${statistics.applications_this_month}`),
            React.createElement('span', { className: 'text-green-600 font-medium' },
              `Offers: ${statistics.status_counts.Offer || 0}`
            ),
            React.createElement('span', { className: 'text-yellow-600 font-medium' },
              `Interviewing: ${statistics.status_counts.Interviewing || 0}`
            )
          )
        ),

        // Actions Section
        React.createElement('div', { className: 'flex flex-col sm:flex-row items-stretch sm:items-center space-y-3 sm:space-y-0 sm:space-x-4' },
          // View toggle
          React.createElement('div', { className: 'flex bg-gray-100 rounded-lg p-1 mobile-full' },
            React.createElement('button', {
              onClick: () => setView('table'),
              className: `flex-1 sm:flex-none px-3 py-2 rounded text-sm font-medium transition-all duration-200 ${view === 'table'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-500 hover:text-gray-700'
                }`
            }, 'Table'),
            React.createElement('button', {
              onClick: () => setView('cards'),
              className: `flex-1 sm:flex-none px-3 py-2 rounded text-sm font-medium transition-all duration-200 ${view === 'cards'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-500 hover:text-gray-700'
                }`
            }, 'Cards')
          ),

          // Export button
          React.createElement('button', {
            onClick: onExportCSV,
            disabled: loading,
            className: 'inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 transition-all duration-200 mobile-full touch-target'
          },
            loading ? 'Exporting...' : 'Export CSV'
          ),

          // New job button
          React.createElement('button', {
            onClick: onNewJob,
            disabled: loading,
            className: 'btn-primary disabled:opacity-50 mobile-full touch-target'
          }, 'New Application')
        )
      )
    )
  );
};
