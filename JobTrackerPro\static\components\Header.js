const Header = ({ onNewJob, onExportCSV, loading }) => {
  const { view, setView, jobs } = useAppContext();
  const [statistics, setStatistics] = useState(null);

  useEffect(() => {
    loadStatistics();
  }, [jobs]);

  const loadStatistics = async () => {
    try {
      const response = await api.get('/statistics');
      setStatistics(response.data);
    } catch (err) {
      console.error('Failed to load statistics:', err);
    }
  };

  return React.createElement('header', { className: 'bg-white shadow-sm border-b' },
    React.createElement('div', { className: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8' },
      React.createElement('div', { className: 'flex justify-between items-center py-6' },
        React.createElement('div', null,
          React.createElement('h1', { className: 'text-3xl font-bold text-gray-900' },
            'Job Application Tracker'
          ),
          statistics && React.createElement('div', { className: 'flex space-x-6 mt-2 text-sm text-gray-600' },
            React.createElement('span', null, `Total: ${statistics.total_applications}`),
            React.createElement('span', null, `This Month: ${statistics.applications_this_month}`),
            React.createElement('span', { className: 'text-green-600' },
              `Offers: ${statistics.status_counts.Offer || 0}`
            ),
            React.createElement('span', { className: 'text-yellow-600' },
              `Interviewing: ${statistics.status_counts.Interviewing || 0}`
            )
          )
        ),

        React.createElement('div', { className: 'flex items-center space-x-4' },
          // View toggle
          React.createElement('div', { className: 'flex bg-gray-100 rounded-lg p-1' },
            React.createElement('button', {
              onClick: () => setView('table'),
              className: `px-3 py-1 rounded text-sm font-medium ${view === 'table'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
                }`
            }, 'Table'),
            React.createElement('button', {
              onClick: () => setView('cards'),
              className: `px-3 py-1 rounded text-sm font-medium ${view === 'cards'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
                }`
            }, 'Cards')
          ),

          // Export button
          React.createElement('button', {
            onClick: onExportCSV,
            disabled: loading,
            className: 'inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50'
          },
            loading ? 'Exporting...' : 'Export CSV'
          ),

          // New job button
          React.createElement('button', {
            onClick: onNewJob,
            disabled: loading,
            className: 'btn-primary disabled:opacity-50'
          }, 'New Application')
        )
      )
    )
  );
};
