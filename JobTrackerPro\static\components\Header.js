const Header = ({ onNewJob, onExportCSV, loading }) => {
  const { view, setView, jobs } = useAppContext();
  const [statistics, setStatistics] = useState(null);
  const [theme, setTheme] = useState('light');

  useEffect(() => {
    loadStatistics();
  }, [jobs]);

  useEffect(() => {
    // Initialize theme state
    if (window.ThemeManager) {
      setTheme(window.ThemeManager.getTheme());

      // Listen for theme changes
      const handleThemeChange = (newTheme) => {
        setTheme(newTheme);
      };

      window.ThemeManager.onThemeChange(handleThemeChange);

      // Cleanup listener on unmount
      return () => {
        window.ThemeManager.offThemeChange(handleThemeChange);
      };
    }
  }, []);

  const loadStatistics = async () => {
    try {
      const response = await api.get('/statistics');
      setStatistics(response.data);
    } catch (err) {
      console.error('Failed to load statistics:', err);
    }
  };

  const handleThemeToggle = () => {
    if (window.ThemeManager) {
      window.ThemeManager.toggleTheme();
    }
  };

  return React.createElement('header', {
    className: 'shadow-sm border-b sticky top-0 z-50 transition-all duration-300',
    style: {
      backgroundColor: 'hsl(var(--card))',
      borderColor: 'hsl(var(--border))'
    }
  },
    React.createElement('div', { className: 'container-responsive' },
      React.createElement('div', { className: 'flex flex-col lg:flex-row lg:justify-between lg:items-center py-4 lg:py-6 space-y-4 lg:space-y-0' },
        // Title and Statistics Section
        React.createElement('div', { className: 'flex-1' },
          React.createElement('h1', {
            className: 'text-2xl lg:text-3xl font-bold mb-2 lg:mb-0 transition-colors duration-300',
            style: { color: 'hsl(var(--foreground))' }
          },
            'Job Application Tracker'
          ),
          statistics && React.createElement('div', {
            className: 'grid grid-cols-2 lg:flex lg:space-x-6 gap-2 lg:gap-0 mt-2 text-sm transition-colors duration-300',
            style: { color: 'hsl(var(--muted-foreground))' }
          },
            React.createElement('span', { className: 'font-medium' }, `Total: ${statistics.total_applications}`),
            React.createElement('span', { className: 'font-medium' }, `This Month: ${statistics.applications_this_month}`),
            React.createElement('span', {
              className: 'font-medium',
              style: { color: 'hsl(var(--secondary))' }
            },
              `Offers: ${statistics.status_counts.Offer || 0}`
            ),
            React.createElement('span', {
              className: 'font-medium',
              style: { color: 'hsl(var(--warning))' }
            },
              `Interviewing: ${statistics.status_counts.Interviewing || 0}`
            )
          )
        ),

        // Actions Section
        React.createElement('div', { className: 'flex flex-col sm:flex-row items-stretch sm:items-center space-y-3 sm:space-y-0 sm:space-x-4' },
          // Theme toggle
          React.createElement('button', {
            onClick: handleThemeToggle,
            className: 'theme-toggle px-3 py-2 rounded-md text-sm font-medium mobile-full touch-target',
            title: window.ThemeManager ? window.ThemeManager.getThemeLabel(theme) : 'Toggle theme',
            'aria-label': window.ThemeManager ? window.ThemeManager.getThemeLabel(theme) : 'Toggle theme'
          },
            React.createElement('span', { className: 'flex items-center justify-center gap-2' },
              React.createElement('span', { className: 'text-lg' },
                window.ThemeManager ? window.ThemeManager.getThemeIcon(theme) : '🌙'
              ),
              React.createElement('span', { className: 'hidden sm:inline' },
                window.ThemeManager ? window.ThemeManager.getThemeLabel(theme) : 'Dark Mode'
              )
            )
          ),

          // View toggle
          React.createElement('div', {
            className: 'flex rounded-lg p-1 mobile-full transition-colors duration-300',
            style: { backgroundColor: 'hsl(var(--muted))' }
          },
            React.createElement('button', {
              onClick: () => setView('table'),
              className: `flex-1 sm:flex-none px-3 py-2 rounded text-sm font-medium transition-all duration-200 ${view === 'table' ? 'shadow-sm' : ''}`,
              style: view === 'table'
                ? { backgroundColor: 'hsl(var(--card))', color: 'hsl(var(--card-foreground))' }
                : { color: 'hsl(var(--muted-foreground))' }
            }, 'Table'),
            React.createElement('button', {
              onClick: () => setView('cards'),
              className: `flex-1 sm:flex-none px-3 py-2 rounded text-sm font-medium transition-all duration-200 ${view === 'cards' ? 'shadow-sm' : ''}`,
              style: view === 'cards'
                ? { backgroundColor: 'hsl(var(--card))', color: 'hsl(var(--card-foreground))' }
                : { color: 'hsl(var(--muted-foreground))' }
            }, 'Cards')
          ),

          // Export button
          React.createElement('button', {
            onClick: onExportCSV,
            disabled: loading,
            className: 'inline-flex items-center justify-center px-4 py-2 rounded-md shadow-sm text-sm font-medium disabled:opacity-50 transition-all duration-200 mobile-full touch-target',
            style: {
              backgroundColor: 'hsl(var(--card))',
              color: 'hsl(var(--card-foreground))',
              border: '1px solid hsl(var(--border))'
            }
          },
            loading ? 'Exporting...' : 'Export CSV'
          ),

          // New job button
          React.createElement('button', {
            onClick: onNewJob,
            disabled: loading,
            className: 'btn-primary disabled:opacity-50 mobile-full touch-target'
          }, 'New Application')
        )
      )
    )
  );
};
