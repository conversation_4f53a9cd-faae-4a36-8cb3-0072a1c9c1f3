@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");

:root {
  /* Light Theme Colors - HSL format for proper CSS variable usage */
  --primary: 213 94% 68%; /* #2563EB */
  --secondary: 158 64% 52%; /* #10B981 */
  --warning: 43 96% 56%; /* #F59E0B */
  --danger: 0 84% 60%; /* #EF4444 */
  --background: 210 40% 98%; /* #F8FAFC */
  --foreground: 215 25% 27%; /* #1E293B */
  --muted: 210 40% 96%; /* #F1F5F9 */
  --border: 214 32% 91%; /* #E2E8F0 */
  --input: 214 32% 91%; /* #E2E8F0 */
  --card: 0 0% 100%; /* #FFFFFF */
  --card-foreground: 215 25% 27%; /* #1E293B */
  --popover: 0 0% 100%; /* #FFFFFF */
  --popover-foreground: 215 25% 27%; /* #1E293B */
  --primary-foreground: 0 0% 100%; /* #FFFFFF */
  --secondary-foreground: 0 0% 100%; /* #FFFFFF */
  --muted-foreground: 215 16% 47%; /* #64748B */
  --accent: 210 40% 94%; /* #F1F5F9 */
  --accent-foreground: 215 25% 27%; /* #1E293B */
  --destructive: 0 84% 60%; /* #EF4444 */
  --destructive-foreground: 0 0% 100%; /* #FFFFFF */
  --ring: 213 94% 68%; /* #2563EB */
  --radius: 0.5rem;

  /* Enhanced spacing and sizing variables */
  --header-height: 4rem;
  --mobile-header-height: 3.5rem;
  --container-padding: 1rem;
  --container-padding-lg: 2rem;

  /* Animation variables */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  --transition-theme: 300ms ease-in-out;
}

/* Dark Theme Colors */
[data-theme="dark"] {
  --primary: 213 94% 68%; /* #2563EB - Keep primary blue */
  --secondary: 158 64% 52%; /* #10B981 - Keep secondary green */
  --warning: 43 96% 56%; /* #F59E0B - Keep warning orange */
  --danger: 0 84% 60%; /* #EF4444 - Keep danger red */
  --background: 222 84% 5%; /* #0F172A - Dark slate */
  --foreground: 210 40% 98%; /* #F8FAFC - Light text */
  --muted: 217 32% 17%; /* #1E293B - Dark muted */
  --border: 217 32% 17%; /* #1E293B - Dark border */
  --input: 217 32% 17%; /* #1E293B - Dark input background */
  --card: 222 84% 5%; /* #0F172A - Dark card background */
  --card-foreground: 210 40% 98%; /* #F8FAFC - Light card text */
  --popover: 222 84% 5%; /* #0F172A - Dark popover */
  --popover-foreground: 210 40% 98%; /* #F8FAFC - Light popover text */
  --primary-foreground: 0 0% 100%; /* #FFFFFF - Keep white */
  --secondary-foreground: 0 0% 100%; /* #FFFFFF - Keep white */
  --muted-foreground: 215 20% 65%; /* #94A3B8 - Lighter muted text */
  --accent: 217 32% 17%; /* #1E293B - Dark accent */
  --accent-foreground: 210 40% 98%; /* #F8FAFC - Light accent text */
  --destructive: 0 84% 60%; /* #EF4444 - Keep danger red */
  --destructive-foreground: 0 0% 100%; /* #FFFFFF - Keep white */
  --ring: 213 94% 68%; /* #2563EB - Keep primary blue */
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color var(--transition-theme),
    color var(--transition-theme);
}

/* Theme toggle button styles */
.theme-toggle {
  background-color: hsl(var(--muted));
  color: hsl(var(--muted-foreground));
  border: 1px solid hsl(var(--border));
  transition: all var(--transition-theme);
}

.theme-toggle:hover {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.theme-toggle:focus {
  outline: none;
  ring: 2px solid hsl(var(--ring));
  ring-offset: 2px;
}

/* Dark mode specific adjustments */
[data-theme="dark"] .theme-toggle {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

[data-theme="dark"] .theme-toggle:hover {
  background-color: hsl(var(--muted));
  color: hsl(var(--muted-foreground));
}

/* Enhanced responsive container */
.container-responsive {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

@media (min-width: 1024px) {
  .container-responsive {
    padding: 0 var(--container-padding-lg);
  }
}

.status-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.status-applied {
  @apply bg-blue-100 text-blue-800;
}

.status-interviewing {
  @apply bg-yellow-100 text-yellow-800;
}

.status-offer {
  @apply bg-green-100 text-green-800;
}

.status-rejected {
  @apply bg-red-100 text-red-800;
}

.status-withdrawn {
  @apply bg-gray-100 text-gray-800;
}

.btn-primary {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  @apply px-4 py-2 rounded-md font-medium hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2;
  min-height: 2.5rem;
  touch-action: manipulation;
  transition: all var(--transition-theme), opacity var(--transition-fast);
  border: 1px solid hsl(var(--primary));
}

.btn-primary:focus {
  ring-color: hsl(var(--ring));
}

.btn-secondary {
  background-color: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
  @apply px-4 py-2 rounded-md font-medium hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2;
  min-height: 2.5rem;
  touch-action: manipulation;
  transition: all var(--transition-theme), opacity var(--transition-fast);
  border: 1px solid hsl(var(--secondary));
}

.btn-secondary:focus {
  ring-color: hsl(var(--secondary));
}

.btn-danger {
  background-color: hsl(var(--danger));
  color: hsl(var(--destructive-foreground));
  @apply px-4 py-2 rounded-md font-medium hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2;
  min-height: 2.5rem;
  touch-action: manipulation;
  transition: all var(--transition-theme), opacity var(--transition-fast);
  border: 1px solid hsl(var(--danger));
}

.btn-danger:focus {
  ring-color: hsl(var(--danger));
}

/* Enhanced button styles for mobile */
@media (max-width: 767px) {
  .btn-primary,
  .btn-secondary,
  .btn-danger {
    @apply px-6 py-3 text-sm;
    min-height: 3rem;
  }
}

.input {
  background-color: hsl(var(--input));
  border: 1px solid hsl(var(--border));
  color: hsl(var(--foreground));
  @apply px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:border-transparent;
  transition: all var(--transition-theme);
}

.input:focus {
  ring-color: hsl(var(--ring));
  background-color: hsl(var(--background));
}

.tag {
  @apply inline-flex items-center px-2 py-1 rounded-md text-xs font-medium;
  transition: all var(--transition-theme);
}

.tag:hover {
  @apply opacity-80;
}

.table-row {
  transition: background-color var(--transition-theme);
}

.table-row:hover {
  background-color: hsl(var(--muted));
}

.form-group {
  @apply mb-4;
}

.form-label {
  color: hsl(var(--foreground));
  @apply block text-sm font-medium mb-2;
  transition: color var(--transition-theme);
}

.form-input {
  background-color: hsl(var(--input));
  border: 1px solid hsl(var(--border));
  color: hsl(var(--foreground));
  @apply w-full px-3 py-2 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:border-transparent;
  min-height: 2.5rem;
  transition: all var(--transition-theme);
}

.form-input:focus {
  ring-color: hsl(var(--ring));
  background-color: hsl(var(--background));
}

.form-textarea {
  background-color: hsl(var(--input));
  border: 1px solid hsl(var(--border));
  color: hsl(var(--foreground));
  @apply w-full px-3 py-2 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:border-transparent resize-vertical;
  min-height: 4rem;
  transition: all var(--transition-theme);
}

.form-textarea:focus {
  ring-color: hsl(var(--ring));
  background-color: hsl(var(--background));
}

.form-select {
  background-color: hsl(var(--input));
  border: 1px solid hsl(var(--border));
  color: hsl(var(--foreground));
  @apply w-full px-3 py-2 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:border-transparent;
  min-height: 2.5rem;
  transition: all var(--transition-theme);
}

.form-select:focus {
  ring-color: hsl(var(--ring));
  background-color: hsl(var(--background));
}

/* Enhanced mobile form styles */
@media (max-width: 767px) {
  .form-input,
  .form-select {
    @apply px-4 py-3 text-base;
    min-height: 3rem;
  }

  .form-textarea {
    @apply px-4 py-3 text-base;
    min-height: 5rem;
  }

  .form-label {
    @apply text-base font-medium mb-2;
  }
}

/* Responsive utility classes */
.mobile-stack {
  @apply flex flex-col space-y-3;
}

@media (min-width: 768px) {
  .mobile-stack {
    @apply flex-row space-y-0 space-x-4;
  }
}

.mobile-full {
  @apply w-full;
}

@media (min-width: 768px) {
  .mobile-full {
    @apply w-auto;
  }
}

/* Enhanced card styles */
.card {
  background-color: hsl(var(--card));
  color: hsl(var(--card-foreground));
  border: 1px solid hsl(var(--border));
  @apply rounded-lg shadow-sm;
  transition: all var(--transition-theme), transform var(--transition-fast),
    box-shadow var(--transition-fast);
}

.card:hover {
  @apply shadow-md;
  transform: translateY(-1px);
}

/* Mobile-optimized table */
.table-responsive {
  @apply overflow-x-auto;
}

@media (max-width: 767px) {
  .table-responsive table {
    min-width: 600px;
  }
}

/* Loading and animation improvements */
.loading {
  @apply animate-pulse;
}

.fade-in {
  animation: fadeIn var(--transition-normal);
}

.slide-up {
  animation: slideUp var(--transition-normal);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Touch-friendly improvements */
@media (max-width: 767px) {
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  .table-row {
    @apply cursor-pointer;
  }
}
