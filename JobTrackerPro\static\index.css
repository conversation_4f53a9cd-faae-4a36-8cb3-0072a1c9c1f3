@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");

:root {
  /* Custom Colors - HSL format for proper CSS variable usage */
  --primary: 213 94% 68%; /* #2563EB */
  --secondary: 158 64% 52%; /* #10B981 */
  --warning: 43 96% 56%; /* #F59E0B */
  --danger: 0 84% 60%; /* #EF4444 */
  --background: 210 40% 98%; /* #F8FAFC */
  --foreground: 215 25% 27%; /* #1E293B */
  --muted: 210 40% 96%; /* #F1F5F9 */
  --border: 214 32% 91%; /* #E2E8F0 */
  --input: 214 32% 91%; /* #E2E8F0 */
  --card: 0 0% 100%; /* #FFFFFF */
  --card-foreground: 215 25% 27%; /* #1E293B */
  --popover: 0 0% 100%; /* #FFFFFF */
  --popover-foreground: 215 25% 27%; /* #1E293B */
  --primary-foreground: 0 0% 100%; /* #FFFFFF */
  --secondary-foreground: 0 0% 100%; /* #FFFFFF */
  --muted-foreground: 215 16% 47%; /* #64748B */
  --accent: 210 40% 94%; /* #F1F5F9 */
  --accent-foreground: 215 25% 27%; /* #1E293B */
  --destructive: 0 84% 60%; /* #EF4444 */
  --destructive-foreground: 0 0% 100%; /* #FFFFFF */
  --ring: 213 94% 68%; /* #2563EB */
  --radius: 0.5rem;

  /* Enhanced spacing and sizing variables */
  --header-height: 4rem;
  --mobile-header-height: 3.5rem;
  --container-padding: 1rem;
  --container-padding-lg: 2rem;

  /* Animation variables */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced responsive container */
.container-responsive {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

@media (min-width: 1024px) {
  .container-responsive {
    padding: 0 var(--container-padding-lg);
  }
}

.status-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.status-applied {
  @apply bg-blue-100 text-blue-800;
}

.status-interviewing {
  @apply bg-yellow-100 text-yellow-800;
}

.status-offer {
  @apply bg-green-100 text-green-800;
}

.status-rejected {
  @apply bg-red-100 text-red-800;
}

.status-withdrawn {
  @apply bg-gray-100 text-gray-800;
}

.btn-primary {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  @apply px-4 py-2 rounded-md font-medium transition-all duration-200 hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  min-height: 2.5rem;
  touch-action: manipulation;
}

.btn-secondary {
  background-color: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
  @apply px-4 py-2 rounded-md font-medium transition-all duration-200 hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2;
  min-height: 2.5rem;
  touch-action: manipulation;
}

.btn-danger {
  background-color: hsl(var(--danger));
  color: hsl(var(--destructive-foreground));
  @apply px-4 py-2 rounded-md font-medium transition-all duration-200 hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
  min-height: 2.5rem;
  touch-action: manipulation;
}

/* Enhanced button styles for mobile */
@media (max-width: 767px) {
  .btn-primary,
  .btn-secondary,
  .btn-danger {
    @apply px-6 py-3 text-sm;
    min-height: 3rem;
  }
}

.input {
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--input));
  color: hsl(var(--foreground));
  @apply px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
}

.tag {
  @apply inline-flex items-center px-2 py-1 rounded-md text-xs font-medium transition-all duration-200;
}

.tag:hover {
  @apply opacity-80;
}

.table-row:hover {
  background-color: hsl(var(--muted));
  transition: background-color var(--transition-fast);
}

.form-group {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200;
  min-height: 2.5rem;
}

.form-textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical transition-all duration-200;
  min-height: 4rem;
}

.form-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white transition-all duration-200;
  min-height: 2.5rem;
}

/* Enhanced mobile form styles */
@media (max-width: 767px) {
  .form-input,
  .form-select {
    @apply px-4 py-3 text-base;
    min-height: 3rem;
  }

  .form-textarea {
    @apply px-4 py-3 text-base;
    min-height: 5rem;
  }

  .form-label {
    @apply text-base font-medium mb-2;
  }
}

/* Responsive utility classes */
.mobile-stack {
  @apply flex flex-col space-y-3;
}

@media (min-width: 768px) {
  .mobile-stack {
    @apply flex-row space-y-0 space-x-4;
  }
}

.mobile-full {
  @apply w-full;
}

@media (min-width: 768px) {
  .mobile-full {
    @apply w-auto;
  }
}

/* Enhanced card styles */
.card {
  background-color: hsl(var(--card));
  color: hsl(var(--card-foreground));
  border: 1px solid hsl(var(--border));
  @apply rounded-lg shadow-sm transition-all duration-200;
}

.card:hover {
  @apply shadow-md;
  transform: translateY(-1px);
}

/* Mobile-optimized table */
.table-responsive {
  @apply overflow-x-auto;
}

@media (max-width: 767px) {
  .table-responsive table {
    min-width: 600px;
  }
}

/* Loading and animation improvements */
.loading {
  @apply animate-pulse;
}

.fade-in {
  animation: fadeIn var(--transition-normal);
}

.slide-up {
  animation: slideUp var(--transition-normal);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Touch-friendly improvements */
@media (max-width: 767px) {
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  .table-row {
    @apply cursor-pointer;
  }
}
