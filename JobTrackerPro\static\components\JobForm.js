const JobForm = ({ job, tags, onSubmit, onClose, loading }) => {
  const [formData, setFormData] = useState({
    company: '',
    position: '',
    location: '',
    job_url: '',
    description: '',
    status: 'Applied',
    salary_range: '',
    application_date: new Date().toISOString().split('T')[0],
    notes: '',
    contact_person: '',
    contact_email: '',
    tag_ids: []
  });

  useEffect(() => {
    if (job) {
      setFormData({
        company: job.company || '',
        position: job.position || '',
        location: job.location || '',
        job_url: job.job_url || '',
        description: job.description || '',
        status: job.status || 'Applied',
        salary_range: job.salary_range || '',
        application_date: job.application_date ? job.application_date.split('T')[0] : new Date().toISOString().split('T')[0],
        notes: job.notes || '',
        contact_person: job.contact_person || '',
        contact_email: job.contact_email || '',
        tag_ids: job.tags ? job.tags.map(tag => tag.id) : []
      });
    }
  }, [job]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleTagToggle = (tagId) => {
    setFormData(prev => ({
      ...prev,
      tag_ids: prev.tag_ids.includes(tagId)
        ? prev.tag_ids.filter(id => id !== tagId)
        : [...prev.tag_ids, tagId]
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.company || !formData.position) {
      alert('Company and position are required');
      return;
    }

    console.log('Submitting form data:', formData);
    await onSubmit(formData);
  };

  const statusOptions = [
    'Applied',
    'Interviewing',
    'Offer',
    'Rejected',
    'Withdrawn'
  ];

  return React.createElement('div', { className: 'fixed inset-0 bg-black bg-opacity-50 flex items-start sm:items-center justify-center p-2 sm:p-4 z-50 overflow-y-auto' },
    React.createElement('div', { className: 'bg-white rounded-lg shadow-xl max-w-2xl w-full my-4 sm:my-0 max-h-screen sm:max-h-[90vh] overflow-y-auto slide-up' },
      React.createElement('div', { className: 'sticky top-0 bg-white px-4 sm:px-6 py-4 border-b border-gray-200 rounded-t-lg' },
        React.createElement('div', { className: 'flex items-center justify-between' },
          React.createElement('h3', { className: 'text-lg font-medium text-gray-900' },
            job ? 'Edit Job Application' : 'New Job Application'
          ),
          React.createElement('button', {
            type: 'button',
            onClick: onClose,
            className: 'text-gray-400 hover:text-gray-600 transition-colors duration-200 touch-target'
          }, '✕')
        )
      ),

      React.createElement('form', { onSubmit: handleSubmit, className: 'px-4 sm:px-6 py-4' },
        React.createElement('div', { className: 'grid grid-cols-1 lg:grid-cols-2 gap-4' },
          // Company
          React.createElement('div', { className: 'form-group' },
            React.createElement('label', { className: 'form-label' }, 'Company *'),
            React.createElement('input', {
              type: 'text',
              name: 'company',
              value: formData.company,
              onChange: handleChange,
              className: 'form-input',
              required: true
            })
          ),

          // Position
          React.createElement('div', { className: 'form-group' },
            React.createElement('label', { className: 'form-label' }, 'Position *'),
            React.createElement('input', {
              type: 'text',
              name: 'position',
              value: formData.position,
              onChange: handleChange,
              className: 'form-input',
              required: true
            })
          ),

          // Location
          React.createElement('div', { className: 'form-group' },
            React.createElement('label', { className: 'form-label' }, 'Location'),
            React.createElement('input', {
              type: 'text',
              name: 'location',
              value: formData.location,
              onChange: handleChange,
              className: 'form-input'
            })
          ),

          // Status
          React.createElement('div', { className: 'form-group' },
            React.createElement('label', { className: 'form-label' }, 'Status'),
            React.createElement('select', {
              name: 'status',
              value: formData.status,
              onChange: handleChange,
              className: 'form-select'
            },
              statusOptions.map(status =>
                React.createElement('option', { key: status, value: status }, status)
              )
            )
          ),

          // Application Date
          React.createElement('div', { className: 'form-group' },
            React.createElement('label', { className: 'form-label' }, 'Application Date'),
            React.createElement('input', {
              type: 'date',
              name: 'application_date',
              value: formData.application_date,
              onChange: handleChange,
              className: 'form-input'
            })
          ),

          // Salary Range
          React.createElement('div', { className: 'form-group' },
            React.createElement('label', { className: 'form-label' }, 'Salary Range'),
            React.createElement('input', {
              type: 'text',
              name: 'salary_range',
              value: formData.salary_range,
              onChange: handleChange,
              placeholder: 'e.g., $80,000 - $100,000',
              className: 'form-input'
            })
          ),

          // Contact Person
          React.createElement('div', { className: 'form-group' },
            React.createElement('label', { className: 'form-label' }, 'Contact Person'),
            React.createElement('input', {
              type: 'text',
              name: 'contact_person',
              value: formData.contact_person,
              onChange: handleChange,
              className: 'form-input'
            })
          ),

          // Contact Email
          React.createElement('div', { className: 'form-group' },
            React.createElement('label', { className: 'form-label' }, 'Contact Email'),
            React.createElement('input', {
              type: 'email',
              name: 'contact_email',
              value: formData.contact_email,
              onChange: handleChange,
              className: 'form-input'
            })
          )
        ),

        // Job URL
        React.createElement('div', { className: 'form-group' },
          React.createElement('label', { className: 'form-label' }, 'Job URL'),
          React.createElement('input', {
            type: 'url',
            name: 'job_url',
            value: formData.job_url,
            onChange: handleChange,
            className: 'form-input'
          })
        ),

        // Description
        React.createElement('div', { className: 'form-group' },
          React.createElement('label', { className: 'form-label' }, 'Job Description'),
          React.createElement('textarea', {
            name: 'description',
            value: formData.description,
            onChange: handleChange,
            rows: 4,
            className: 'form-textarea'
          })
        ),

        // Notes
        React.createElement('div', { className: 'form-group' },
          React.createElement('label', { className: 'form-label' }, 'Notes'),
          React.createElement('textarea', {
            name: 'notes',
            value: formData.notes,
            onChange: handleChange,
            rows: 3,
            className: 'form-textarea'
          })
        ),

        // Tags
        React.createElement('div', { className: 'form-group' },
          React.createElement('label', { className: 'form-label' }, 'Tags'),
          React.createElement('div', { className: 'flex flex-wrap gap-2 mt-1' },
            tags.map(tag =>
              React.createElement('button', {
                key: tag.id,
                type: 'button',
                onClick: () => handleTagToggle(tag.id),
                className: `tag ${formData.tag_ids.includes(tag.id)
                  ? 'ring-2 ring-blue-500'
                  : 'hover:opacity-80'
                  }`,
                style: {
                  backgroundColor: `${tag.color}20`,
                  color: tag.color,
                  border: `1px solid ${tag.color}40`
                }
              }, tag.name)
            ),
            tags.length === 0 && React.createElement('span', {
              className: 'text-sm text-gray-500'
            }, 'No tags available')
          )
        ),

        React.createElement('div', { className: 'sticky bottom-0 bg-white px-4 sm:px-6 py-4 border-t border-gray-200 flex flex-col sm:flex-row justify-end gap-3 sm:space-x-3 rounded-b-lg' },
          React.createElement('button', {
            type: 'button',
            onClick: onClose,
            className: 'order-2 sm:order-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-all duration-200 touch-target'
          }, 'Cancel'),
          React.createElement('button', {
            type: 'submit',
            disabled: loading,
            className: 'order-1 sm:order-2 btn-primary disabled:opacity-50 touch-target'
          }, loading ? 'Saving...' : (job ? 'Update' : 'Create'))
        )
      )
    )
  );
};
